package com.chatbot.data.services

import ai.koog.prompt.executor.clients.anthropic.AnthropicLLMClient
import ai.koog.prompt.executor.clients.google.GoogleLLMClient
import ai.koog.prompt.executor.clients.openai.OpenAILLMClient
import ai.koog.prompt.llm.LLMCapability
import ai.koog.prompt.llm.LLMProvider
import com.chatbot.data.database.entities.LlmModel
import com.chatbot.data.database.entities.LlmProvider
import com.chatbot.data.database.entities.ProviderType
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class LlmServiceTest {

    // Note: Tests for createLLMClient() are in a separate integration test file
    // as they require actual client instantiation and may need network access.

    @Test
    fun `toKoogLLModel should convert LlmModel to LLModel for OPENAI`() {
        val llmModel = LlmModel(
            id = 1L,
            providerId = 1L,
            modelId = "gpt-4",
            capabilities = "[\"text_generation\", \"function_calling\", \"streaming\"]",
            maxInputTokens = 8192,
            maxOutputTokens = 4096
        )

        val result = llmModel.toKoogLLModel(ProviderType.OPENAI)

        assertEquals(LLMProvider.OpenAI, result.provider)
        assertEquals("gpt-4", result.id)
        assertEquals(8192L, result.contextLength)
        assertEquals(4096L, result.maxOutputTokens)
        assertTrue(result.capabilities.contains(LLMCapability.Temperature))
        assertTrue(result.capabilities.contains(LLMCapability.Completion))
        assertTrue(result.capabilities.contains(LLMCapability.Tools))
        assertTrue(result.capabilities.contains(LLMCapability.ToolChoice))
        assertTrue(result.capabilities.contains(LLMCapability.Audio))
        assertTrue(result.capabilities.contains(LLMCapability.OpenAIEndpoint.Completions))
    }

    @Test
    fun `toKoogLLModel should convert LlmModel to LLModel for ANTHROPIC`() {
        val llmModel = LlmModel(
            id = 1L,
            providerId = 1L,
            modelId = "claude-3-opus",
            capabilities = "[\"text_generation\", \"function_calling\", \"streaming\"]",
            maxInputTokens = 200000,
            maxOutputTokens = 4096
        )

        val result = llmModel.toKoogLLModel(ProviderType.ANTHROPIC)

        assertEquals(LLMProvider.Anthropic, result.provider)
        assertEquals("claude-3-opus", result.id)
        assertEquals(200000L, result.contextLength)
        assertEquals(4096L, result.maxOutputTokens)
    }

    @Test
    fun `toKoogLLModel should convert LlmModel to LLModel for GEMINI`() {
        val llmModel = LlmModel(
            id = 1L,
            providerId = 1L,
            modelId = "gemini-pro",
            capabilities = "[\"text_generation\", \"function_calling\", \"streaming\"]",
            maxInputTokens = 32768,
            maxOutputTokens = 8192
        )

        val result = llmModel.toKoogLLModel(ProviderType.GEMINI)

        assertEquals(LLMProvider.Google, result.provider)
        assertEquals("gemini-pro", result.id)
        assertEquals(32768L, result.contextLength)
        assertEquals(8192L, result.maxOutputTokens)
    }

    @Test
    fun `toKoogLLModel should convert LlmModel to LLModel for OPENAI_COMPATIBLE`() {
        val llmModel = LlmModel(
            id = 1L,
            providerId = 1L,
            modelId = "custom-model",
            capabilities = "[\"text_generation\", \"streaming\"]",
            maxInputTokens = 4096,
            maxOutputTokens = 2048
        )

        val result = llmModel.toKoogLLModel(ProviderType.OPENAI_COMPATIBLE)

        assertEquals(LLMProvider.OpenAI, result.provider)
        assertEquals("custom-model", result.id)
        assertEquals(4096L, result.contextLength)
        assertEquals(2048L, result.maxOutputTokens)
    }
    @Test
    fun `testKoog Provider`() {
        val llmModel = LlmModel(
            id = 1L,
            providerId = 1L,
            modelId = "custom-model",
            capabilities = "[\"text_generation\", \"streaming\"]",
            maxInputTokens = 4096,
            maxOutputTokens = 2048
        )

        val result = llmModel.toKoogLLModel(ProviderType.OPENAI_COMPATIBLE)

        assertEquals(LLMProvider.OpenAI, result.provider)
        assertEquals("custom-model", result.id)
        assertEquals(4096L, result.contextLength)
        assertEquals(2048L, result.maxOutputTokens)
    }

    // Tests for LlmProvider properties and validation

    @Test
    fun `LlmProvider should have correct default values`() {
        val provider = LlmProvider(
            id = 1L,
            name = "Test Provider",
            providerType = ProviderType.OPENAI.value
        )

        assertEquals(60, provider.timeoutSeconds)
        assertEquals(3, provider.maxRetries)
        assertTrue(provider.isActive)
    }

    @Test
    fun `LlmProvider should handle different provider types`() {
        val openaiProvider = LlmProvider(
            id = 1L,
            name = "OpenAI",
            providerType = ProviderType.OPENAI.value
        )

        val anthropicProvider = LlmProvider(
            id = 2L,
            name = "Anthropic",
            providerType = ProviderType.ANTHROPIC.value
        )

        val geminiProvider = LlmProvider(
            id = 3L,
            name = "Gemini",
            providerType = ProviderType.GEMINI.value
        )

        val compatibleProvider = LlmProvider(
            id = 4L,
            name = "Compatible",
            providerType = ProviderType.OPENAI_COMPATIBLE.value
        )

        assertEquals(ProviderType.OPENAI.value, openaiProvider.providerType)
        assertEquals(ProviderType.ANTHROPIC.value, anthropicProvider.providerType)
        assertEquals(ProviderType.GEMINI.value, geminiProvider.providerType)
        assertEquals(ProviderType.OPENAI_COMPATIBLE.value, compatibleProvider.providerType)
    }

    @Test
    fun `ProviderType enum should have correct values`() {
        assertEquals("openai", ProviderType.OPENAI.value)
        assertEquals("anthropic", ProviderType.ANTHROPIC.value)
        assertEquals("gemini", ProviderType.GEMINI.value)
        assertEquals("openai_compatible", ProviderType.OPENAI_COMPATIBLE.value)
    }

    @Test
    fun `ProviderType fromString should work correctly`() {
        assertEquals(ProviderType.OPENAI, ProviderType.fromString("openai"))
        assertEquals(ProviderType.ANTHROPIC, ProviderType.fromString("anthropic"))
        assertEquals(ProviderType.GEMINI, ProviderType.fromString("gemini"))
        assertEquals(ProviderType.OPENAI_COMPATIBLE, ProviderType.fromString("openai_compatible"))
        assertEquals(null, ProviderType.fromString("invalid"))
    }

    @Test
    fun `ProviderType isValidType should work correctly`() {
        assertTrue(ProviderType.isValidType("openai"))
        assertTrue(ProviderType.isValidType("anthropic"))
        assertTrue(ProviderType.isValidType("gemini"))
        assertTrue(ProviderType.isValidType("openai_compatible"))
        assertTrue(!ProviderType.isValidType("invalid"))
    }

}