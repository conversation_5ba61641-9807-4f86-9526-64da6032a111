package com.chatbot.data.services

import ai.koog.prompt.executor.clients.ConnectionTimeoutConfig
import ai.koog.prompt.executor.clients.LLMClient
import ai.koog.prompt.executor.clients.anthropic.AnthropicClientSettings
import ai.koog.prompt.executor.clients.anthropic.AnthropicLLMClient
import ai.koog.prompt.executor.clients.google.GoogleClientSettings
import ai.koog.prompt.executor.clients.google.GoogleLLMClient
import ai.koog.prompt.executor.clients.openai.OpenAIClientSettings
import ai.koog.prompt.executor.clients.openai.OpenAILLMClient
import ai.koog.prompt.executor.clients.openai.OpenAIModels
import ai.koog.prompt.llm.LLMCapability
import ai.koog.prompt.llm.LLMProvider
import ai.koog.prompt.llm.LLModel
import com.chatbot.core.utils.Converters
import com.chatbot.core.utils.jsonToList
import com.chatbot.data.database.entities.LlmModel
import com.chatbot.data.database.entities.LlmProvider
import com.chatbot.data.database.entities.ProviderType
import io.ktor.client.HttpClient
import io.ktor.client.engine.ProxyBuilder
import io.ktor.client.engine.http

/**
 * LLM服务接口
 *
 * 基于Koog.ai API设计的LLM服务层，提供统一的LLM交互接口
 * 集成Prompt API和Tool API，支持多提供商和多模态功能
 */
fun LlmProvider.createLLMClient(): LLMClient {
    val httpClient = HttpClient() {
        if (baseUrl?.isNotEmpty() == true) {
            engine {
                proxy = ProxyBuilder.http(proxyUrl ?: "")
            }
        }
    }
    val timeoutConfig = ConnectionTimeoutConfig(
        requestTimeoutMillis = timeoutSeconds.toLong(),
        connectTimeoutMillis = timeoutSeconds.toLong(),
        socketTimeoutMillis = timeoutSeconds.toLong()
    )
    when (providerType) {
        ProviderType.GEMINI.value -> {
            val googleClientSettings = GoogleClientSettings(timeoutConfig = timeoutConfig)
            return  GoogleLLMClient(apiKey = apiKey ?: "", googleClientSettings, httpClient)
        }
        ProviderType.ANTHROPIC.value -> {
            val anthropicClientSettings = AnthropicClientSettings(timeoutConfig = timeoutConfig)
            return AnthropicLLMClient(apiKey = apiKey ?: "", anthropicClientSettings, httpClient)
        }
        else -> {
            val openAIClientSettings = OpenAIClientSettings(
                baseUrl = baseUrl ?: "https://api.openai.com",
                timeoutConfig = timeoutConfig
            )
            return OpenAILLMClient(apiKey = apiKey ?: "", openAIClientSettings, httpClient)
        }
    }
}
fun LlmModel.toKoogLLModel(provider: ProviderType): LLModel {
    OpenAIModels.Reasoning.O3
    val provider = when (provider) {
        ProviderType.OPENAI -> LLMProvider.OpenAI
        ProviderType.ANTHROPIC -> LLMProvider.Anthropic
        ProviderType.GEMINI -> LLMProvider.Google
        ProviderType.OPENAI_COMPATIBLE -> LLMProvider.OpenAI
    }
    return LLModel(
        provider = provider,
        id = modelId,
        capabilities = capabilities.jsonToList().map { LLMCapability.valueOf(it) },
        contextLength = maxInputLength,
        maxOutputTokens = maxOutputLength,
    )
}